//@version=5
strategy("CVD Divergence Strategy", overlay=true, default_qty_type=strategy.percent_of_equity, default_qty_value=10)

// Strategy Settings
anchorInput = '12M'
useCustomTimeframeInput = false
lowerTimeframeInput = '1'

// ATR Settings for Stop Loss and Take Profit
atr_length = input.int(14, title="ATR Length", minval=1)
risk_reward_ratio = input.float(2.0, title="Risk/Reward Ratio", minval=0.1, step=0.1)

upAndDownVolume() =>
    posVol = 0.0
    negVol = 0.0
    
    var isBuyVolume = true    

    switch
        close > open     => isBuyVolume := true
        close < open     => isBuyVolume := false
        close > close[1] => isBuyVolume := true
        close < close[1] => isBuyVolume := false

    if isBuyVolume
        posVol += volume
    else
        negVol -= volume

    posVol + negVol

var lowerTimeframe = switch
    useCustomTimeframeInput => lowerTimeframeInput
    timeframe.isseconds     => "1S"
    timeframe.isintraday    => "1"
    timeframe.isdaily       => "5"
    => "60"

diffVolArray = request.security_lower_tf(syminfo.tickerid, lowerTimeframe, upAndDownVolume())

getHighLow(arr) =>
    float cumVolume = na
    float maxVolume = na
    float minVolume = na

    for item in arr
        cumVolume := nz(cumVolume) + item
        maxVolume := math.max(nz(maxVolume), cumVolume)
        minVolume := math.min(nz(minVolume), cumVolume)

    [maxVolume, minVolume, cumVolume]

[maxVolume, minVolume, lastVolume] = getHighLow(diffVolArray)

var cumulLastVolume = 0.0
anchorChange = timeframe.change(anchorInput) or (not na(lastVolume) and na(lastVolume[1]))
cumulOpenVolume = anchorChange ? 0.0 : cumulLastVolume[1]
cumulMaxVolume = cumulOpenVolume + maxVolume
cumulMinVolume = cumulOpenVolume + minVolume
cumulLastVolume := cumulOpenVolume + lastVolume

col = cumulLastVolume >= cumulOpenVolume ? color.teal : color.red

var cumVol = 0.
cumVol += nz(volume)
if barstate.islast and cumVol == 0
    runtime.error("The data vendor doesn't provide volume data for this symbol.")

overlay_main = true
osc = cumulLastVolume
lbR = 5
lbL = 5
rangeUpper = 60
rangeLower = 5
plotBull = true
plotHiddenBull = false
plotBear = true
plotHiddenBear = false
delay_plot_til_closed = true
bearColor = color.red
bullColor = color.green
hiddenBullColor = color.new(color.green, 80)
hiddenBearColor = color.new(color.red, 80)
textColor = color.white
noneColor = color.new(color.white, 100)

repaint = (not(delay_plot_til_closed) or barstate.ishistory or barstate.isconfirmed)

plFound = na(ta.pivotlow(osc, lbL, lbR)) ? false : true
phFound = na(ta.pivothigh(osc, lbL, lbR)) ? false : true
_inRange(cond) =>
	bars = ta.barssince(cond == true)
	rangeLower <= bars and bars <= rangeUpper

//------------------------------------------------------------------------------
// Regular Bullish
// Osc: Higher Low

oscHL = osc[lbR] > ta.valuewhen(plFound, osc[lbR], 1) and _inRange(plFound[1])

// Price: Lower Low

priceLL = low[lbR] < ta.valuewhen(plFound, low[lbR], 1)
bullCond = plotBull and priceLL and oscHL and plFound and repaint

plot(
     plFound ? overlay_main ? low[lbR] : osc[lbR] : na,
     offset=-lbR,
     title="Regular Bullish",
     linewidth=2,
     color=(bullCond ? bullColor : noneColor)
     )

plotshape(
	 bullCond ? overlay_main ? low[lbR] : osc[lbR] : na,
	 offset=-lbR,
	 title="Regular Bullish Label",
	 text=" Bull ",
	 style=shape.labelup,
	 location=location.absolute,
	 color=bullColor,
	 textcolor=textColor
	 )

//------------------------------------------------------------------------------
// Regular Bearish
// Osc: Lower High

oscLH = osc[lbR] < ta.valuewhen(phFound, osc[lbR], 1) and _inRange(phFound[1])

// Price: Higher High

priceHH = high[lbR] > ta.valuewhen(phFound, high[lbR], 1)

bearCond = plotBear and priceHH and oscLH and phFound and repaint

plot(
	 phFound ? overlay_main ? high[lbR] : osc[lbR] : na,
	 offset=-lbR,
	 title="Regular Bearish",
	 linewidth=2,
	 color=(bearCond ? bearColor : noneColor)
	 )

plotshape(
	 bearCond ? overlay_main ? high[lbR] : osc[lbR] : na,
	 offset=-lbR,
	 title="Regular Bearish Label",
	 text=" Bear ",
	 style=shape.labeldown,
	 location=location.absolute,
	 color=bearColor,
	 textcolor=textColor
	 )

// Strategy Logic
// Calculate ATR
atr_value = ta.atr(atr_length)

// Bull signal (Long entry) - Enter on next bar after signal
if bullCond[lbR] and barstate.isconfirmed
    entry_price = close
    stop_loss = entry_price - atr_value
    take_profit = entry_price + (atr_value * risk_reward_ratio)
    strategy.entry("Long", strategy.long, comment="Bull Signal")
    strategy.exit("Long Exit", "Long", stop=stop_loss, limit=take_profit, comment="SL/TP")

// Bear signal (Short entry) - Enter on next bar after signal
if bearCond[lbR] and barstate.isconfirmed
    entry_price = close
    stop_loss = entry_price + atr_value
    take_profit = entry_price - (atr_value * risk_reward_ratio)
    strategy.entry("Short", strategy.short, comment="Bear Signal")
    strategy.exit("Short Exit", "Short", stop=stop_loss, limit=take_profit, comment="SL/TP")

// Plot entry levels for visualization (optional)
var line entry_line = na
var line sl_line = na
var line tp_line = na

if strategy.position_size != 0 and strategy.position_size != strategy.position_size[1]
    if not na(entry_line)
        line.delete(entry_line)
    if not na(sl_line)
        line.delete(sl_line)
    if not na(tp_line)
        line.delete(tp_line)
    
    entry_line := line.new(bar_index, strategy.position_avg_price, bar_index + 10, strategy.position_avg_price, color=color.blue, width=2)
    
    if strategy.position_size > 0  // Long position
        sl_line := line.new(bar_index, strategy.position_avg_price - atr_value, bar_index + 10, strategy.position_avg_price - atr_value, color=color.red, width=1)
        tp_line := line.new(bar_index, strategy.position_avg_price + (atr_value * risk_reward_ratio), bar_index + 10, strategy.position_avg_price + (atr_value * risk_reward_ratio), color=color.green, width=1)
    else  // Short position
        sl_line := line.new(bar_index, strategy.position_avg_price + atr_value, bar_index + 10, strategy.position_avg_price + atr_value, color=color.red, width=1)
        tp_line := line.new(bar_index, strategy.position_avg_price - (atr_value * risk_reward_ratio), bar_index + 10, strategy.position_avg_price - (atr_value * risk_reward_ratio), color=color.green, width=1)
