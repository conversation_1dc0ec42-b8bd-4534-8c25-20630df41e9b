#!/bin/bash

# CVD 策略系統部署腳本

echo "🚀 CVD 策略系統部署腳本"
echo "=========================="

# 檢查 Docker 是否安裝
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安裝，請先安裝 Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose 未安裝，請先安裝 Docker Compose"
    exit 1
fi

# 檢查環境變量文件
if [ ! -f ".env" ]; then
    echo "⚠️ 未找到 .env 文件，正在創建..."
    cp cvd_env_example .env
    echo "📝 請編輯 .env 文件設置您的 Telegram 配置:"
    echo "   - TELEGRAM_BOT_TOKEN"
    echo "   - TELEGRAM_CHAT_ID"
    echo ""
    echo "設置完成後請重新運行此腳本"
    exit 1
fi

# 檢查環境變量是否設置
source .env
if [ -z "$TELEGRAM_BOT_TOKEN" ] || [ -z "$TELEGRAM_CHAT_ID" ]; then
    echo "❌ 請在 .env 文件中設置 TELEGRAM_BOT_TOKEN 和 TELEGRAM_CHAT_ID"
    exit 1
fi

echo "✅ 環境變量檢查通過"

# 創建必要的目錄
echo "📁 創建數據目錄..."
mkdir -p data
mkdir -p logs

# 構建 Docker 鏡像
echo "🔨 構建 Docker 鏡像..."
docker build -f cvd_dockerfile -t cvd-strategy:latest .

if [ $? -ne 0 ]; then
    echo "❌ Docker 鏡像構建失敗"
    exit 1
fi

echo "✅ Docker 鏡像構建成功"

# 停止現有容器（如果存在）
echo "🛑 停止現有容器..."
docker-compose -f cvd_docker_compose.yml down

# 啟動服務
echo "🚀 啟動 CVD 策略系統..."
docker-compose -f cvd_docker_compose.yml up -d

if [ $? -eq 0 ]; then
    echo "✅ CVD 策略系統部署成功！"
    echo ""
    echo "📊 系統狀態:"
    docker-compose -f cvd_docker_compose.yml ps
    echo ""
    echo "📋 查看日誌:"
    echo "   docker-compose -f cvd_docker_compose.yml logs -f"
    echo ""
    echo "🛑 停止系統:"
    echo "   docker-compose -f cvd_docker_compose.yml down"
else
    echo "❌ 系統啟動失敗"
    exit 1
fi
