"""
CVD 策略核心模塊 - 雲端版本
實時監控多幣種 CVD 背離信號並生成交易信號

支持幣種: 1000PEPE, TRB, DOGE, SOL, BTC
時間框架: 15分鐘
盈虧比: 2:1

作者: 專業量化策略工程師
"""

import asyncio
import aiohttp
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import logging
import json
from dataclasses import dataclass

@dataclass
class TradingSignal:
    """交易信號數據類"""
    symbol: str
    direction: str  # LONG or SHORT
    entry_price: float
    take_profit: float
    stop_loss: float
    timestamp: datetime
    atr_value: float
    signal_id: str

class CVDIndicator:
    """CVD 指標計算器"""
    
    def __init__(self, lbL: int = 5, lbR: int = 5, rangeUpper: int = 60, rangeLower: int = 5):
        self.lbL = lbL
        self.lbR = lbR
        self.rangeUpper = rangeUpper
        self.rangeLower = rangeLower
    
    def calculate_volume_delta(self, df: pd.DataFrame) -> pd.Series:
        """計算成交量差值"""
        volume_delta = []
        
        for i in range(len(df)):
            is_buy_volume = True
            
            if df['close'].iloc[i] > df['open'].iloc[i]:
                is_buy_volume = True
            elif df['close'].iloc[i] < df['open'].iloc[i]:
                is_buy_volume = False
            elif i > 0:
                if df['close'].iloc[i] > df['close'].iloc[i-1]:
                    is_buy_volume = True
                elif df['close'].iloc[i] < df['close'].iloc[i-1]:
                    is_buy_volume = False
            
            if is_buy_volume:
                delta = df['volume'].iloc[i]
            else:
                delta = -df['volume'].iloc[i]
            
            volume_delta.append(delta)
        
        return pd.Series(volume_delta, index=df.index)
    
    def calculate_cvd(self, df: pd.DataFrame) -> pd.Series:
        """計算累積成交量差值"""
        volume_delta = self.calculate_volume_delta(df)
        return volume_delta.cumsum()
    
    def find_pivot_points(self, series: pd.Series, left: int, right: int) -> Tuple[pd.Series, pd.Series]:
        """尋找樞軸點"""
        pivot_highs = pd.Series(index=series.index, dtype=float)
        pivot_lows = pd.Series(index=series.index, dtype=float)

        for i in range(left, len(series) - right):
            # 檢查高點 - 當前點必須高於左右兩邊的所有點
            is_high = True
            for j in range(i - left, i + right + 1):
                if j != i and j >= 0 and j < len(series):
                    if series.iloc[j] >= series.iloc[i]:
                        is_high = False
                        break
            if is_high:
                pivot_highs.iloc[i] = series.iloc[i]

            # 檢查低點 - 當前點必須低於左右兩邊的所有點
            is_low = True
            for j in range(i - left, i + right + 1):
                if j != i and j >= 0 and j < len(series):
                    if series.iloc[j] <= series.iloc[i]:
                        is_low = False
                        break
            if is_low:
                pivot_lows.iloc[i] = series.iloc[i]

        return pivot_highs, pivot_lows
    
    def detect_divergences(self, df: pd.DataFrame) -> Tuple[pd.Series, pd.Series]:
        """檢測背離信號"""
        cvd = self.calculate_cvd(df)
        cvd_highs, cvd_lows = self.find_pivot_points(cvd, self.lbL, self.lbR)
        
        bull_signals = pd.Series(False, index=df.index)
        bear_signals = pd.Series(False, index=df.index)
        
        # 檢測牛市背離
        for i in range(self.lbL + self.lbR, len(df)):
            current_idx = i - self.lbR
            
            if pd.notna(cvd_lows.iloc[current_idx]):
                prev_cvd_low_idx = None
                for j in range(current_idx - 1, -1, -1):
                    if pd.notna(cvd_lows.iloc[j]):
                        bars_since = current_idx - j
                        if self.rangeLower <= bars_since <= self.rangeUpper:
                            prev_cvd_low_idx = j
                            break
                
                if prev_cvd_low_idx is not None:
                    cvd_higher_low = cvd.iloc[current_idx] > cvd.iloc[prev_cvd_low_idx]
                    price_lower_low = df['low'].iloc[current_idx] < df['low'].iloc[prev_cvd_low_idx]
                    
                    if cvd_higher_low and price_lower_low:
                        bull_signals.iloc[current_idx] = True
        
        # 檢測熊市背離
        for i in range(self.lbL + self.lbR, len(df)):
            current_idx = i - self.lbR
            
            if pd.notna(cvd_highs.iloc[current_idx]):
                prev_cvd_high_idx = None
                for j in range(current_idx - 1, -1, -1):
                    if pd.notna(cvd_highs.iloc[j]):
                        bars_since = current_idx - j
                        if self.rangeLower <= bars_since <= self.rangeUpper:
                            prev_cvd_high_idx = j
                            break
                
                if prev_cvd_high_idx is not None:
                    cvd_lower_high = cvd.iloc[current_idx] < cvd.iloc[prev_cvd_high_idx]
                    price_higher_high = df['high'].iloc[current_idx] > df['high'].iloc[prev_cvd_high_idx]
                    
                    if cvd_lower_high and price_higher_high:
                        bear_signals.iloc[current_idx] = True
        
        return bull_signals, bear_signals

class CVDStrategyCore:
    """CVD 策略核心系統"""
    
    def __init__(self):
        self.symbols = ["1000PEPEUSDT", "TRBUSDT", "DOGEUSDT", "SOLUSDT", "BTCUSDT"]
        self.base_url = "https://fapi.binance.com"
        self.cvd_indicator = CVDIndicator()
        
        # 設置日誌
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
        # 設置為INFO級別以顯示更多信息
        self.logger.setLevel(logging.INFO)
        
        # 存儲歷史數據
        self.historical_data = {}
        self.session = None
    
    async def __aenter__(self):
        """異步上下文管理器"""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """異步上下文管理器"""
        if self.session:
            await self.session.close()
    
    async def fetch_kline_data(self, symbol: str, limit: int = 200) -> Optional[pd.DataFrame]:
        """獲取K線數據"""
        try:
            url = f"{self.base_url}/fapi/v1/klines"
            params = {
                "symbol": symbol,
                "interval": "15m",
                "limit": limit
            }
            
            async with self.session.get(url, params=params, timeout=30) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    if data:
                        df = pd.DataFrame(data, columns=[
                            'timestamp', 'open', 'high', 'low', 'close', 'volume',
                            'close_time', 'quote_asset_volume', 'number_of_trades',
                            'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
                        ])
                        
                        # 轉換數據類型
                        for col in ['open', 'high', 'low', 'close', 'volume']:
                            df[col] = pd.to_numeric(df[col])
                        
                        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                        df.set_index('timestamp', inplace=True)
                        df = df.sort_index()
                        
                        return df
                
                self.logger.error(f"❌ {symbol} API響應異常: {response.status}")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ {symbol} 數據獲取失敗: {e}")
            return None
    
    def calculate_atr(self, df: pd.DataFrame, period: int = 14) -> pd.Series:
        """計算ATR"""
        high = df['high']
        low = df['low']
        close = df['close']
        
        tr1 = high - low
        tr2 = abs(high - close.shift(1))
        tr3 = abs(low - close.shift(1))
        
        true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = true_range.rolling(window=period).mean()
        
        return atr
    
    def generate_signal_id(self, symbol: str, timestamp: datetime) -> str:
        """生成信號ID"""
        return f"{symbol}_{timestamp.strftime('%Y%m%d_%H%M%S')}"
    
    async def check_signals(self, symbol: str) -> Optional[TradingSignal]:
        """檢查單個幣種的信號"""
        try:
            # 獲取最新數據
            df = await self.fetch_kline_data(symbol)
            if df is None or len(df) < 100:
                self.logger.warning(f"⚠️ {symbol} 數據不足，跳過檢測")
                return None

            # 檢測背離信號
            bull_signals, bear_signals = self.cvd_indicator.detect_divergences(df)

            # 計算ATR
            atr = self.calculate_atr(df)

            # 添加調試信息
            bull_count = bull_signals.sum()
            bear_count = bear_signals.sum()
            self.logger.info(f"🔍 {symbol} - 牛市信號: {bull_count}, 熊市信號: {bear_count}")
            
            # 檢查最近幾根K線的信號（不只是最新一根）
            signal = None

            # 檢查最近3根K線是否有信號
            for check_idx in range(max(0, len(df) - 3), len(df)):
                current_time = df.index[check_idx]
                current_price = df['close'].iloc[check_idx]
                current_atr = atr.iloc[check_idx]

                if pd.isna(current_atr):
                    continue

                # 檢查牛市信號
                if bull_signals.iloc[check_idx]:
                    signal = TradingSignal(
                        symbol=symbol,
                        direction="LONG",
                        entry_price=current_price,
                        take_profit=current_price + (2.0 * current_atr),  # 2:1 盈虧比
                        stop_loss=current_price - (1.0 * current_atr),
                        timestamp=current_time,
                        atr_value=current_atr,
                        signal_id=self.generate_signal_id(symbol, current_time)
                    )
                    break  # 找到信號就跳出

                # 檢查熊市信號
                elif bear_signals.iloc[check_idx]:
                    signal = TradingSignal(
                        symbol=symbol,
                        direction="SHORT",
                        entry_price=current_price,
                        take_profit=current_price - (2.0 * current_atr),  # 2:1 盈虧比
                        stop_loss=current_price + (1.0 * current_atr),
                        timestamp=current_time,
                        atr_value=current_atr,
                        signal_id=self.generate_signal_id(symbol, current_time)
                    )
                    break  # 找到信號就跳出
            
            if signal:
                self.logger.info(f"🎯 {symbol} 檢測到 {signal.direction} 信號")
                return signal
            
            return None
            
        except Exception as e:
            self.logger.error(f"❌ {symbol} 信號檢測失敗: {e}")
            return None
    
    async def scan_all_symbols(self) -> List[TradingSignal]:
        """掃描所有幣種的信號"""
        self.logger.info("🔍 開始掃描所有幣種信號...")
        
        signals = []
        
        for symbol in self.symbols:
            try:
                signal = await self.check_signals(symbol)
                if signal:
                    signals.append(signal)
                
                # 避免請求過於頻繁
                await asyncio.sleep(0.5)
                
            except Exception as e:
                self.logger.error(f"❌ {symbol} 掃描失敗: {e}")
        
        self.logger.info(f"✅ 掃描完成，發現 {len(signals)} 個信號")
        return signals
    
    def format_signal_for_telegram(self, signal: TradingSignal) -> str:
        """格式化信號為Telegram消息"""
        symbol_display = signal.symbol.replace("USDT", "/USDT").replace("1000", "")
        
        message = f"""⚡合約預言機 

💰 幣種: {symbol_display}
📈 方向: {signal.direction}
💵 入場價: ${signal.entry_price:.4f}

🎯 止盈價: ${signal.take_profit:.4f}
🛑 止損價: ${signal.stop_loss:.4f}"""
        
        return message

async def test_cvd_core():
    """測試CVD核心系統"""
    print("🧪 測試 CVD 策略核心系統...")
    
    async with CVDStrategyCore() as core:
        # 測試單個幣種
        signal = await core.check_signals("BTCUSDT")
        if signal:
            print("✅ 檢測到信號:")
            print(core.format_signal_for_telegram(signal))
        else:
            print("ℹ️ 當前無信號")
        
        # 測試所有幣種掃描
        all_signals = await core.scan_all_symbols()
        print(f"📊 總共檢測到 {len(all_signals)} 個信號")

if __name__ == "__main__":
    asyncio.run(test_cvd_core())
