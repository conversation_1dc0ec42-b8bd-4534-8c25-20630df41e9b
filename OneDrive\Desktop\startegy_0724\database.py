"""
CVD 策略數據庫模塊
管理交易信號、交易結果、統計數據等

作者: 專業量化策略工程師
"""

import sqlite3
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
import logging
import json
from dataclasses import dataclass, asdict
from cvd_strategy_core import TradingSignal

@dataclass
class TradeResult:
    """交易結果數據類"""
    signal_id: str
    symbol: str
    direction: str
    entry_price: float
    exit_price: float
    entry_time: datetime
    exit_time: datetime
    result_type: str  # "TAKE_PROFIT" or "STOP_LOSS"
    pnl_percentage: float
    duration_minutes: int

class CVDDatabase:
    """CVD 策略數據庫管理器"""
    
    def __init__(self, db_path: str = "cvd_strategy.db"):
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        self.init_database()
    
    def init_database(self):
        """初始化數據庫表"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 創建信號表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS signals (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        signal_id TEXT UNIQUE NOT NULL,
                        symbol TEXT NOT NULL,
                        direction TEXT NOT NULL,
                        entry_price REAL NOT NULL,
                        take_profit REAL NOT NULL,
                        stop_loss REAL NOT NULL,
                        atr_value REAL NOT NULL,
                        timestamp DATETIME NOT NULL,
                        status TEXT DEFAULT 'ACTIVE',
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # 創建交易結果表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS trade_results (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        signal_id TEXT NOT NULL,
                        symbol TEXT NOT NULL,
                        direction TEXT NOT NULL,
                        entry_price REAL NOT NULL,
                        exit_price REAL NOT NULL,
                        entry_time DATETIME NOT NULL,
                        exit_time DATETIME NOT NULL,
                        result_type TEXT NOT NULL,
                        pnl_percentage REAL NOT NULL,
                        duration_minutes INTEGER NOT NULL,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (signal_id) REFERENCES signals (signal_id)
                    )
                """)
                
                # 創建每日統計表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS daily_stats (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        date DATE UNIQUE NOT NULL,
                        total_signals INTEGER DEFAULT 0,
                        take_profit_count INTEGER DEFAULT 0,
                        stop_loss_count INTEGER DEFAULT 0,
                        win_rate REAL DEFAULT 0.0,
                        total_pnl_percentage REAL DEFAULT 0.0,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                conn.commit()
                self.logger.info("✅ 數據庫初始化完成")
                
        except Exception as e:
            self.logger.error(f"❌ 數據庫初始化失敗: {e}")
            raise
    
    def save_signal(self, signal: TradingSignal) -> bool:
        """保存交易信號"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    INSERT OR REPLACE INTO signals 
                    (signal_id, symbol, direction, entry_price, take_profit, stop_loss, atr_value, timestamp)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    signal.signal_id,
                    signal.symbol,
                    signal.direction,
                    signal.entry_price,
                    signal.take_profit,
                    signal.stop_loss,
                    signal.atr_value,
                    signal.timestamp
                ))
                
                conn.commit()
                self.logger.info(f"✅ 信號已保存: {signal.signal_id}")
                return True
                
        except Exception as e:
            self.logger.error(f"❌ 信號保存失敗: {e}")
            return False
    
    def save_trade_result(self, trade_result: TradeResult) -> bool:
        """保存交易結果"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 保存交易結果
                cursor.execute("""
                    INSERT INTO trade_results 
                    (signal_id, symbol, direction, entry_price, exit_price, entry_time, exit_time, 
                     result_type, pnl_percentage, duration_minutes)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    trade_result.signal_id,
                    trade_result.symbol,
                    trade_result.direction,
                    trade_result.entry_price,
                    trade_result.exit_price,
                    trade_result.entry_time,
                    trade_result.exit_time,
                    trade_result.result_type,
                    trade_result.pnl_percentage,
                    trade_result.duration_minutes
                ))
                
                # 更新信號狀態
                cursor.execute("""
                    UPDATE signals SET status = 'COMPLETED' WHERE signal_id = ?
                """, (trade_result.signal_id,))
                
                conn.commit()
                self.logger.info(f"✅ 交易結果已保存: {trade_result.signal_id}")
                return True
                
        except Exception as e:
            self.logger.error(f"❌ 交易結果保存失敗: {e}")
            return False
    
    def get_active_signals(self) -> List[Dict]:
        """獲取活躍信號"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT * FROM signals WHERE status = 'ACTIVE' ORDER BY timestamp DESC
                """)
                
                columns = [description[0] for description in cursor.description]
                results = []
                
                for row in cursor.fetchall():
                    results.append(dict(zip(columns, row)))
                
                return results
                
        except Exception as e:
            self.logger.error(f"❌ 獲取活躍信號失敗: {e}")
            return []
    
    def get_trade_statistics(self) -> Dict:
        """獲取交易統計數據"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 總體統計
                cursor.execute("""
                    SELECT 
                        COUNT(*) as total_trades,
                        SUM(CASE WHEN result_type = 'TAKE_PROFIT' THEN 1 ELSE 0 END) as take_profit_count,
                        SUM(CASE WHEN result_type = 'STOP_LOSS' THEN 1 ELSE 0 END) as stop_loss_count,
                        AVG(CASE WHEN result_type = 'TAKE_PROFIT' THEN 1.0 ELSE 0.0 END) as win_rate,
                        SUM(pnl_percentage) as total_pnl
                    FROM trade_results
                """)
                
                result = cursor.fetchone()
                
                if result and result[0] > 0:
                    stats = {
                        'total_signals': result[0],
                        'take_profit_count': result[1] or 0,
                        'stop_loss_count': result[2] or 0,
                        'win_rate': result[3] or 0.0,
                        'total_pnl': result[4] or 0.0
                    }
                else:
                    stats = {
                        'total_signals': 0,
                        'take_profit_count': 0,
                        'stop_loss_count': 0,
                        'win_rate': 0.0,
                        'total_pnl': 0.0
                    }
                
                return stats
                
        except Exception as e:
            self.logger.error(f"❌ 獲取統計數據失敗: {e}")
            return {
                'total_signals': 0,
                'take_profit_count': 0,
                'stop_loss_count': 0,
                'win_rate': 0.0,
                'total_pnl': 0.0
            }
    
    def get_recent_trades(self, limit: int = 10) -> List[Dict]:
        """獲取最近的交易記錄"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT * FROM trade_results 
                    ORDER BY exit_time DESC 
                    LIMIT ?
                """, (limit,))
                
                columns = [description[0] for description in cursor.description]
                results = []
                
                for row in cursor.fetchall():
                    results.append(dict(zip(columns, row)))
                
                return results
                
        except Exception as e:
            self.logger.error(f"❌ 獲取最近交易失敗: {e}")
            return []
    
    def update_daily_stats(self):
        """更新每日統計"""
        try:
            today = datetime.now().date()
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 計算今日統計
                cursor.execute("""
                    SELECT 
                        COUNT(*) as total_signals,
                        SUM(CASE WHEN result_type = 'TAKE_PROFIT' THEN 1 ELSE 0 END) as take_profit_count,
                        SUM(CASE WHEN result_type = 'STOP_LOSS' THEN 1 ELSE 0 END) as stop_loss_count,
                        SUM(pnl_percentage) as total_pnl
                    FROM trade_results 
                    WHERE DATE(exit_time) = ?
                """, (today,))
                
                result = cursor.fetchone()
                
                if result and result[0] > 0:
                    total_signals = result[0]
                    take_profit_count = result[1] or 0
                    stop_loss_count = result[2] or 0
                    win_rate = take_profit_count / total_signals if total_signals > 0 else 0.0
                    total_pnl = result[3] or 0.0
                    
                    # 插入或更新每日統計
                    cursor.execute("""
                        INSERT OR REPLACE INTO daily_stats 
                        (date, total_signals, take_profit_count, stop_loss_count, win_rate, total_pnl_percentage)
                        VALUES (?, ?, ?, ?, ?, ?)
                    """, (today, total_signals, take_profit_count, stop_loss_count, win_rate, total_pnl))
                    
                    conn.commit()
                    self.logger.info(f"✅ 每日統計已更新: {today}")
                
        except Exception as e:
            self.logger.error(f"❌ 更新每日統計失敗: {e}")
    
    def cleanup_old_data(self, days: int = 30):
        """清理舊數據"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 清理舊的已完成信號
                cursor.execute("""
                    DELETE FROM signals 
                    WHERE status = 'COMPLETED' AND timestamp < ?
                """, (cutoff_date,))
                
                # 清理舊的每日統計
                cursor.execute("""
                    DELETE FROM daily_stats 
                    WHERE date < ?
                """, (cutoff_date.date(),))
                
                conn.commit()
                self.logger.info(f"✅ 清理了 {days} 天前的舊數據")
                
        except Exception as e:
            self.logger.error(f"❌ 清理舊數據失敗: {e}")

def test_database():
    """測試數據庫功能"""
    print("🧪 測試數據庫功能...")
    
    db = CVDDatabase("test_cvd.db")
    
    # 測試保存信號
    from cvd_strategy_core import TradingSignal
    test_signal = TradingSignal(
        symbol="BTCUSDT",
        direction="LONG",
        entry_price=50000.0,
        take_profit=51000.0,
        stop_loss=49000.0,
        timestamp=datetime.now(),
        atr_value=500.0,
        signal_id="TEST_001"
    )
    
    success = db.save_signal(test_signal)
    print(f"保存信號: {'✅' if success else '❌'}")
    
    # 測試獲取活躍信號
    active_signals = db.get_active_signals()
    print(f"活躍信號數量: {len(active_signals)}")
    
    # 測試統計數據
    stats = db.get_trade_statistics()
    print(f"統計數據: {stats}")

if __name__ == "__main__":
    test_database()
