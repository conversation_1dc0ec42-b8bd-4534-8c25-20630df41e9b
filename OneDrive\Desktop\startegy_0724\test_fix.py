"""
測試修復後的CVD策略系統
"""

import asyncio
import logging
from cvd_strategy_core import CVDStrategyCore

async def test_strategy():
    """測試策略系統"""
    print("🧪 測試修復後的CVD策略系統...")
    
    # 設置詳細日誌
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    async with CVDStrategyCore() as core:
        print("📊 開始掃描所有幣種...")
        
        # 測試每個幣種
        for symbol in core.symbols:
            print(f"\n🔍 檢測 {symbol}...")
            signal = await core.check_signals(symbol)
            
            if signal:
                print("✅ 發現信號!")
                print(core.format_signal_for_telegram(signal))
            else:
                print("ℹ️ 當前無信號")
            
            # 避免請求過於頻繁
            await asyncio.sleep(1)
        
        print("\n📈 完整掃描測試...")
        all_signals = await core.scan_all_symbols()
        print(f"🎯 總共檢測到 {len(all_signals)} 個信號")
        
        for signal in all_signals:
            print("\n" + "="*50)
            print(core.format_signal_for_telegram(signal))

if __name__ == "__main__":
    asyncio.run(test_strategy())
