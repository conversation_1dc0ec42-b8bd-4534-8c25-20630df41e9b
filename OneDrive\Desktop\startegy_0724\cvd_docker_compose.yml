version: '3.8'

services:
  cvd-strategy:
    build:
      context: .
      dockerfile: cvd_dockerfile
    container_name: cvd-strategy-system
    restart: unless-stopped
    environment:
      - TELEGRAM_BOT_TOKEN=${TELEGRAM_BOT_TOKEN}
      - TELEGRAM_CHAT_ID=${TELEGRAM_CHAT_ID}
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    networks:
      - cvd-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

networks:
  cvd-network:
    driver: bridge

volumes:
  cvd-data:
    driver: local
