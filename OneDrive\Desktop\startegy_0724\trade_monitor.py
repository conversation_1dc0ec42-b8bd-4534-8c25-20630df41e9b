"""
交易監控系統
獨立監控每筆交易的止盈止損結果

作者: 專業量化策略工程師
"""

import asyncio
import aiohttp
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import logging
from database import CVDDatabase, TradeResult
import json

class TradeMonitor:
    """交易監控器"""
    
    def __init__(self, db: CVDDatabase):
        self.db = db
        self.base_url = "https://fapi.binance.com"
        self.session = None
        
        # 設置日誌
        self.logger = logging.getLogger(__name__)
        
        # 監控中的交易
        self.monitoring_trades = {}
    
    async def __aenter__(self):
        """異步上下文管理器"""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """異步上下文管理器"""
        if self.session:
            await self.session.close()
    
    async def get_current_price(self, symbol: str) -> Optional[float]:
        """獲取當前價格"""
        try:
            url = f"{self.base_url}/fapi/v1/ticker/price"
            params = {"symbol": symbol}
            
            async with self.session.get(url, params=params, timeout=10) as response:
                if response.status == 200:
                    data = await response.json()
                    return float(data['price'])
                
                self.logger.error(f"❌ {symbol} 價格獲取失敗: {response.status}")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ {symbol} 價格獲取異常: {e}")
            return None
    
    def check_trade_exit(self, signal: Dict, current_price: float) -> Optional[str]:
        """檢查交易是否應該平倉"""
        direction = signal['direction']
        entry_price = signal['entry_price']
        take_profit = signal['take_profit']
        stop_loss = signal['stop_loss']
        
        if direction == "LONG":
            if current_price >= take_profit:
                return "TAKE_PROFIT"
            elif current_price <= stop_loss:
                return "STOP_LOSS"
        else:  # SHORT
            if current_price <= take_profit:
                return "TAKE_PROFIT"
            elif current_price >= stop_loss:
                return "STOP_LOSS"
        
        return None
    
    def calculate_pnl_percentage(self, signal: Dict, exit_price: float, exit_type: str) -> float:
        """計算盈虧百分比"""
        direction = signal['direction']
        entry_price = signal['entry_price']
        
        if direction == "LONG":
            pnl_pct = ((exit_price - entry_price) / entry_price) * 100
        else:  # SHORT
            pnl_pct = ((entry_price - exit_price) / entry_price) * 100
        
        # 考慮10倍槓桿
        return pnl_pct * 10
    
    def calculate_duration(self, entry_time: str, exit_time: datetime) -> int:
        """計算持倉時間（分鐘）"""
        try:
            entry_dt = pd.to_datetime(entry_time)
            duration = exit_time - entry_dt
            return int(duration.total_seconds() / 60)
        except:
            return 0
    
    def format_duration(self, minutes: int) -> str:
        """格式化持倉時間"""
        if minutes < 60:
            return f"{minutes}m"
        else:
            hours = minutes // 60
            mins = minutes % 60
            return f"{hours}h {mins}m"
    
    async def monitor_single_trade(self, signal: Dict) -> Optional[TradeResult]:
        """監控單筆交易"""
        signal_id = signal['signal_id']
        symbol = signal['symbol']
        
        try:
            while True:
                # 獲取當前價格
                current_price = await self.get_current_price(symbol)
                if current_price is None:
                    await asyncio.sleep(30)  # 30秒後重試
                    continue
                
                # 檢查是否觸發止盈止損
                exit_type = self.check_trade_exit(signal, current_price)
                
                if exit_type:
                    # 交易結束
                    exit_time = datetime.now()
                    pnl_pct = self.calculate_pnl_percentage(signal, current_price, exit_type)
                    duration = self.calculate_duration(signal['timestamp'], exit_time)
                    
                    trade_result = TradeResult(
                        signal_id=signal_id,
                        symbol=symbol,
                        direction=signal['direction'],
                        entry_price=signal['entry_price'],
                        exit_price=current_price,
                        entry_time=pd.to_datetime(signal['timestamp']),
                        exit_time=exit_time,
                        result_type=exit_type,
                        pnl_percentage=pnl_pct,
                        duration_minutes=duration
                    )
                    
                    # 保存交易結果
                    success = self.db.save_trade_result(trade_result)
                    if success:
                        self.logger.info(f"✅ {signal_id} 交易完成: {exit_type}")
                        return trade_result
                    else:
                        self.logger.error(f"❌ {signal_id} 結果保存失敗")
                        return None
                
                # 等待下次檢查
                await asyncio.sleep(60)  # 每分鐘檢查一次
                
        except Exception as e:
            self.logger.error(f"❌ {signal_id} 監控異常: {e}")
            return None
    
    async def start_monitoring(self):
        """開始監控系統"""
        self.logger.info("🚀 交易監控系統啟動")
        
        while True:
            try:
                # 獲取所有活躍信號
                active_signals = self.db.get_active_signals()
                
                # 檢查新的信號
                for signal in active_signals:
                    signal_id = signal['signal_id']
                    
                    # 如果信號還沒有被監控，開始監控
                    if signal_id not in self.monitoring_trades:
                        self.logger.info(f"🎯 開始監控交易: {signal_id}")
                        
                        # 創建監控任務
                        task = asyncio.create_task(self.monitor_single_trade(signal))
                        self.monitoring_trades[signal_id] = {
                            'task': task,
                            'signal': signal,
                            'start_time': datetime.now()
                        }
                
                # 清理已完成的監控任務
                completed_tasks = []
                for signal_id, monitor_info in self.monitoring_trades.items():
                    if monitor_info['task'].done():
                        completed_tasks.append(signal_id)
                        
                        # 獲取任務結果
                        try:
                            result = monitor_info['task'].result()
                            if result:
                                self.logger.info(f"✅ {signal_id} 監控完成")
                        except Exception as e:
                            self.logger.error(f"❌ {signal_id} 監控任務異常: {e}")
                
                # 移除已完成的任務
                for signal_id in completed_tasks:
                    del self.monitoring_trades[signal_id]
                
                # 等待下次掃描
                await asyncio.sleep(30)  # 每30秒掃描一次新信號
                
            except Exception as e:
                self.logger.error(f"❌ 監控系統異常: {e}")
                await asyncio.sleep(60)  # 出錯後等待1分鐘
    
    def format_trade_result_for_telegram(self, trade_result: TradeResult) -> str:
        """格式化交易結果為Telegram消息"""
        symbol_display = trade_result.symbol.replace("USDT", "/USDT").replace("1000", "")
        
        # 結算類型中文
        result_type_cn = "止盈" if trade_result.result_type == "TAKE_PROFIT" else "止損"
        
        # 盈虧顏色
        pnl_color = "🟢" if trade_result.pnl_percentage > 0 else "🔴"
        
        message = f"""🛑 交易結算

{pnl_color} 幣種: {symbol_display}
⏰ 時框: 15m
📈 方向: {trade_result.direction}

💵 入場價: ${trade_result.entry_price:.4f}
💰 出場價: ${trade_result.exit_price:.4f}
📊 結算類型: {result_type_cn} 

💸 盈虧: {trade_result.pnl_percentage:+.2f}% 
⏰ 持倉時間: {self.format_duration(trade_result.duration_minutes)}"""
        
        return message
    
    def get_monitoring_status(self) -> Dict:
        """獲取監控狀態"""
        return {
            'active_monitors': len(self.monitoring_trades),
            'monitoring_signals': list(self.monitoring_trades.keys())
        }

async def test_trade_monitor():
    """測試交易監控系統"""
    print("🧪 測試交易監控系統...")
    
    db = CVDDatabase("test_cvd.db")
    
    async with TradeMonitor(db) as monitor:
        # 測試獲取價格
        price = await monitor.get_current_price("BTCUSDT")
        print(f"BTC 當前價格: ${price}")
        
        # 測試監控狀態
        status = monitor.get_monitoring_status()
        print(f"監控狀態: {status}")

if __name__ == "__main__":
    asyncio.run(test_trade_monitor())
